package config

import (
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/env"
)

type LogSt struct {
	Level string `env:"LOG_LEVEL" default:"info"`
}

type DBRWSt struct {
	User            string        `env:"DB_RW_USER" default:"postgres"`
	Password        string        `env:"DB_RW_PASSWORD" default:"123456"`
	Host            string        `env:"DB_RW_HOST" default:"127.0.0.1"`
	Port            uint16        `env:"DB_RW_PORT" default:"5432"`
	Name            string        `env:"DB_RW_NAME" default:"postgres"`
	MaxConns        int32         `env:"DB_RW_MAX_OPEN_CONNS" default:"32"`
	MaxConnLifeTime time.Duration `env:"DB_RW_MAX_CONN_LIFE_TIME" default:"1h"`
	MaxConnIdleTime time.Duration `env:"DB_RW_MAX_CONN_IDLE_TIME" default:"30m"`
}

type DBROSt struct {
	User            string        `env:"DB_RO_USER" default:"postgres"`
	Password        string        `env:"DB_RO_PASSWORD" default:"123456"`
	Host            string        `env:"DB_RO_HOST" default:"127.0.0.1"`
	Port            uint16        `env:"DB_RO_PORT" default:"5432"`
	Name            string        `env:"DB_RO_NAME" default:"postgres"`
	MaxConns        int32         `env:"DB_RO_MAX_OPEN_CONNS" default:"32"`
	MaxConnLifeTime time.Duration `env:"DB_RO_MAX_CONN_LIFE_TIME" default:"1h"`
	MaxConnIdleTime time.Duration `env:"DB_RO_MAX_CONN_IDLE_TIME" default:"30m"`
	LogDBName       string        `env:"LOG_DB_NAME" default:"postgres"`
}

type RedisSt struct {
	Host            string        `env:"REDIS_HOST" default:"127.0.0.1:6379"`
	Password        string        `env:"REDIS_PASSWORD" default:""`
	Network         string        `env:"REDIS_NETWORK" default:"tcp"`
	DB              int           `env:"REDIS_DB" default:"0"`
	MaxIdleConn     int           `env:"REDIS_MAX_IDLE_CONN" default:"32"`
	MaxActiveConn   int           `env:"REDIS_MAX_ACTIVE_CONN" default:"32"`
	ConnIdleTimeout time.Duration `env:"REDIS_CONN_IDLE_TIMEOUT" default:"1m"`
	ConnWait        bool          `env:"REDIS_CONN_WAIT" default:"true"`
	MaxConnLifetime time.Duration `env:"REDIS_MAX_LIFE_TIME" default:"1m"`
}

type APISt struct {
	Addr string `env:"API_ADDR" default:"127.0.0.1:1501"`
}

type BTFSSt struct {
	Host      string   `env:"BTFS_HOST" default:"http://*************:5001"`
	HostIps   []string `env:"HOST_IPS" default:"http://127.0.0.1:5001,http://**************:5002"`
	S3Host    string   `env:"S3_HOST" default:"http://127.0.0.1:6001"`
	S3HostIps []string `env:"S3_HOST_IPS" default:"http://127.0.0.1:6001,http://**************:6002"`
	VisitType string   `env:"VISIT_TYPE"  default:"ip"` // domain vs ip

	UploadApiPath  string `env:"BTFS_UPLOAD_API_PATH" default:"/api/v1/add"`
	RemoveApiPath  string `env:"BTFS_REMOVE_API_PATH" default:"/api/v1/rm"`
	GenerateKey    string `env:"BTFS_GENERATE_KEY" default:"/api/v1/accesskey/generate"`
	EnableKey      string `env:"BTFS_ENABLE_KEY" default:"/api/v1/accesskey/enable"`
	DisableKey     string `env:"BTFS_DISABLE_KEY" default:"/api/v1/accesskey/disable"`
	DeleteKey      string `env:"BTFS_DELETE_KEY" default:"/api/v1/accesskey/delete"`
	ListKey        string `env:"BTFS_LIST_KEY" default:"/api/v1/accesskey/list"`
	ResetKey       string `env:"RESET_KEY" default:"/api/v1/accesskey/reset"`
	GetKey         string `env:"GET_KEY" default:"/api/v1/accesskey/get"`
	DownloadObject string `env:"BTFS_DOWNLOAD_OBJECT" default:"/api/v1/cat"`
}

type DNSSt struct {
	RootDomain string `env:"DNS_ROOT_DOMAIN" default:"on.bttscan.net"`
	ZoneId     string `env:"DNS_ZONE_ID" default:"Z076524538CTIPKINFEGQ"`
}

type GitHubAppSt struct {
	AppID          int64  `env:"GITHUB_APP_ID" default:"234991"`
	ClientID       string `env:"GITHUB_CLIENT_ID" default:"Iv1.97274d09c6cebc6e"`
	ClientSecret   string `env:"GITHUB_CLIENT_SECRET" default:"6979ce4b8287108663f8465b913ebb065d26136d"`
	PrivateKeyPath string `env:"GITHUB_PRIVATE_KEY_PATH" default:""`
	Endpoint       string `env:"GITHUB_ENDPOINT" default:"https://github.com/login/oauth/access_token"`
}

type KafkaSt struct {
	BootstrapServers []string `env:"KAFKA_BOOTSTRAP_SERVERS" default:"127.0.0.1:9092"`
}

type DeployJobSt struct {
	Topic       string `env:"DEPLOY_JOB_TOPIC" default:"t-deploy"`
	GroupId     string `env:"DEPLOY_JOB_GROUP_ID" default:"g-deploy-v1.0"`
	OffsetReset string `env:"DEPLOY_JOB_OFFSET_RESET" default:"earliest"`
	Routines    int    `env:"DEPLOY_JOB_ROUTINES" default:"2"`
}

type SiteNameSt struct {
	ReservedWords                     map[string]struct{} `env:"SITE_NAME_RESERVED_WORDS" default:"tron,justin,sunyuchen,justinsun,sunjustin,tronscan,justlend,apenft,btfs,trongrid,winklink,juststable,btt,trx,usdd,usdj,usdt,usdc,tusd,jst,wallet,scan"`
	ReservedWordsAllowedUserAddresses map[string]string   `env:"SITE_NAME_RESERVED_WORDS_ALLOWED_USER_ADDRESSES" default:"******************************************:BTTC"`
	UserAddressesWhiteList            map[string]string   `env:"USER_ADDRESSES_WHITELIST" default:"******************************************:BTTC"`
}

type TronSt struct {
	APIHost    string `env:"TRON_API_HOST" default:"https://api.trongrid.io"`
	APIKey     string `env:"TRON_API_KEY" default:"1a42490a-07f7-421a-83ce-63269d35b3a9"`
	IntervalMs int64  `env:"TRON_INTERVAL_MS" default:"100"`
}

type ProfitsSt struct {
	SuperUserAddresses       map[string]string `env:"PROFITS_SUPER_USER_ADDRESSES" default:""`
	FreeStoreSize            env.Size          `env:"PROFITS_FREE_STORE_SIZE" default:"8GB"`
	GoldNFTHolderStoreSize   env.Size          `env:"PROFITS_GOLD_NFT_HOLDER_STORE_SIZE" default:"200GB"`
	SilverNFTHolderStoreSize env.Size          `env:"PROFITS_SILVER_NFT_HOLDER_STORE_SIZE" default:"100GB"`
	BronzeNFTHolderStoreSize env.Size          `env:"PROFITS_BRONZE_NFT_HOLDER_STORE_SIZE" default:"50GB"`
	FreeSiteSize             env.Size          `env:"PROFITS_FREE_SITE_SIZE" default:"100MB"`
	GoldNFTHolderSiteSize    env.Size          `env:"PROFITS_GOLD_HOLDER_SITE_SIZE" default:"500M"`
	SilverNFTHolderSiteSize  env.Size          `env:"PROFITS_SILVER_NFT_HOLDER_SITE_SIZE" default:"300M"`
	BronzeNFTHolderSiteSize  env.Size          `env:"PROFITS_BRONZE_NFT_HOLDER_SITE_SIZE" default:"200M"`
	FreeSiteNum              int               `env:"PROFITS_FREE_SITE_NUM" default:"10"`
	GoldNFTHolderSiteNum     int               `env:"PROFITS_GOLD_NFT_HOLDER_SITE_NUM" default:"60"`
	SilverNFTHolderSiteNum   int               `env:"PROFITS_SILVER_NFT_HOLDER_SITE_NUM" default:"30"`
	BronzeNFTHolderSiteNum   int               `env:"PROFITS_BRONZE_NFT_HOLDER_SITE_NUM" default:"20"`
}

type AwsSt struct {
	OutputBucket          string `env:"AWS_OUTPUT_BUCKET" default:"s3://btfs-logs/"`
	Region                string `env:"AWS_REGION" default:"ap-southeast-1"`
	AccessId              string `env:"AWS_ACCESS_ID" default:""`
	SecretAccessKey       string `env:"AWS_SECRET_ACCESS_KEY,,hide" default:""`
	SessionTokenOnlyLocal string `env:"AWS_SESSION_TOKEN_ONLY_LOCAL,,hide" default:""`
}

type AwsS3St struct {
	Region          string `env:"AWS_S3_REGION" default:"ap-east-1"`
	Bucket          string `env:"AWS_S3_BUCKET" default:"for-docker-hongkong"`
	AccessId        string `env:"AWS_S3_ACCESS_ID" default:"********************"`
	SecretAccessKey string `env:"AWS_S3_SECRET_ACCESS_KEY,,hide" default:"pIvkfO+ck4jsbZmiCvA+04HKYH6Zkz20bt+21UIa"`
}
