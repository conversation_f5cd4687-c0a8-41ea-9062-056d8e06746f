package btfs

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"gitlab.insidebt.net/btfs/storage3-backend/log"
)

type service struct {
	host          string
	uploadApiPath string
	removeApiPath string
}

func newService(host, uploadApiPath, removeApiPath string) *service {
	return &service{
		host:          host,
		uploadApiPath: uploadApiPath,
		removeApiPath: removeApiPath,
	}
}

func (s *service) parseUploadUrl(options *UploadOptions) (uploadUrl string, err error) {
	uri, err := url.Parse(s.host)
	if err != nil {
		return
	}
	uri.Path = s.uploadApiPath
	queries := uri.Query()
	if options.Pin {
		queries.Set("pin", "true")
	}
	uri.RawQuery = queries.Encode()
	uploadUrl = uri.String()
	return
}

func (s *service) UploadItem(ctx context.Context, item string, options *UploadOptions) (result *UploadItemResult, err error) {
	uploadUri, err := s.parseUploadUrl(options)
	if err != nil {
		return
	}

	item = path.Clean(item)
	body := &bytes.Buffer{}
	multi := multipart.NewWriter(body)
	file, err := os.Open(item)
	if err != nil {
		return
	}
	defer file.Close()

	part, err := multi.CreateFormFile("file", path.Base(item))
	if err != nil {
		return
	}
	_, err = io.Copy(part, file)
	err = multi.Close()
	if err != nil {
		return
	}

	results, err := s.doUploadRequest(ctx, uploadUri, multi.FormDataContentType(), body)
	if err != nil {
		return
	}
	if len(results) < 1 {
		err = errors.New("no result")
		return
	}

	result = results[0]
	return
}

// func (s *service) UploadDir(ctx context.Context, dir string, options *UploadOptions) (results []*UploadItemResult, err error) {
// 	uploadUri, err := s.parseUploadUrl(options)
// 	if err != nil {
// 		return
// 	}
//
// 	dir = path.Clean(dir)
// 	reader, writer := io.Pipe()
// 	multi := multipart.NewWriter(writer)
//
// 	// 文件统计
// 	var totalFiles int
// 	var totalSize int64
// 	err = filepath.WalkDir(dir, func(curr string, d fs.DirEntry, err error) error {
// 		if err != nil || d.IsDir() {
// 			return nil
// 		}
//
// 		info, statErr := os.Stat(curr)
// 		if statErr == nil {
// 			totalFiles++
// 			totalSize += info.Size()
// 		}
// 		return nil
// 	})
// 	if err != nil {
// 		return
// 	}
// 	log.Infof("准备上传 %d 个文件，总大小 %.2f MB, publishDir: %s", totalFiles, float64(totalSize)/1024/1024, dir)
//
// 	errChan := make(chan error, 1)
// 	// 开启写入协程
// 	go func() {
// 		defer writer.Close()
// 		defer func() {
// 			if r := recover(); r != nil {
// 				log.Errorf("upload panic: %v", r)
// 				errChan <- fmt.Errorf("panic: %v", r)
// 			}
// 		}()
//
// 		idxDir := strings.LastIndex(dir, "/")
// 		n := 0
// 		multiErr := func() error {
// 			defer multi.Close()
// 			return filepath.WalkDir(dir, func(curr string, d fs.DirEntry, err error) error {
// 				if err := ctx.Err(); err != nil {
// 					return err
// 				}
// 				if d.IsDir() {
// 					return nil
// 				}
//
// 				file, err := os.Open(curr)
// 				if err != nil {
// 					return err
// 				}
// 				defer file.Close()
//
// 				field := "file"
// 				if n > 0 {
// 					field = fmt.Sprintf("file-%d", n)
// 				}
// 				name := curr[idxDir+1:]
// 				// log.Infof("上传文件 [%d/%d]: %s", n+1, totalFiles, name)
// 				part, err := multi.CreateFormFile(field, name)
// 				if err != nil {
// 					return err
// 				}
// 				_, err = io.Copy(part, file)
// 				n++
// 				return err
// 			})
// 		}()
// 		errChan <- multiErr
// 	}()
// 	log.Info("开始发送上传文件request...")
//
// 	results, err = s.doUploadRequest(ctx, uploadUri, multi.FormDataContentType(), reader)
// 	log.Infof("results = %+v, err = %v", results, err)
// 	if err != nil {
// 		return
// 	}
//
// 	walkErr := <-errChan
// 	if walkErr != nil {
// 		err = walkErr
// 		return
// 	}
//
// 	if len(results) < 1 {
// 		err = errors.New("no result")
// 		return
// 	}
// 	log.Infof("成功上传 %d 个文件", len(results))
//
// 	return
// }
//
// func (s *service) doUploadRequest(ctx context.Context, uploadUri string, contentType string, body io.Reader) (results []*UploadItemResult, err error) {
// 	req, err := http.NewRequest("POST", uploadUri, body)
// 	if err != nil {
// 		return
// 	}
// 	req.Header.Set("Content-Type", contentType)
//
// 	timeOut := 10 * time.Minute
// 	if deadline, ok := ctx.Deadline(); ok {
// 		timeOut = deadline.Sub(time.Now())
// 	}
// 	if timeOut <= 0 {
// 		err = errors.New("request timeout")
// 		return
// 	}
//
// 	client := &http.Client{
// 		Timeout: timeOut,
// 		Transport: &http.Transport{
// 			MaxIdleConns:          10,
// 			IdleConnTimeout:       30 * time.Second,
// 			TLSHandshakeTimeout:   10 * time.Second,
// 			ExpectContinueTimeout: 1 * time.Second,
// 		},
// 	}
//
// 	start := time.Now()
// 	resp, err := client.Do(req)
// 	if err != nil {
// 		log.Errorf("上传请求失败: %v", err)
// 		return
// 	}
// 	defer resp.Body.Close()
//
// 	log.Infof("上传请求完成, 用时 %.2fs", time.Since(start).Seconds())
// 	if resp.StatusCode >= 400 {
// 		err = fmt.Errorf("BTFS 返回错误状态码: %d", resp.StatusCode)
// 		return
// 	}
//
// 	// 读取带超时限制
// 	// 动态计算读取超时时间
// 	readTimeout := 10 * time.Minute
// 	if deadline, ok := ctx.Deadline(); ok {
// 		remaining := time.Until(deadline)
// 		if remaining > readTimeout {
// 			readTimeout = remaining
// 		}
// 	}
// 	log.Infof("read response..., timeout is %ds", readTimeout)
// 	readCtx, cancel := context.WithTimeout(ctx, readTimeout)
// 	defer cancel()
//
// 	bodyChan := make(chan []byte, 1)
// 	errChan := make(chan error, 1)
//
// 	go func() {
// 		data, readErr := io.ReadAll(resp.Body)
// 		if readErr != nil {
// 			errChan <- readErr
// 		} else {
// 			bodyChan <- data
// 		}
// 	}()
//
// 	select {
// 	case <-readCtx.Done():
// 		err = errors.New("upload to btfs timeout")
// 		return
// 	case readErr := <-errChan:
// 		err = readErr
// 		return
// 	case bodyBytes := <-bodyChan:
// 		log.Infof("返回内容：%s", string(bodyBytes))
// 		dec := json.NewDecoder(bytes.NewReader(bodyBytes))
// 		for {
// 			var result UploadItemResult
// 			if decodeErr := dec.Decode(&result); decodeErr == io.EOF {
// 				break
// 			} else if decodeErr != nil {
// 				err = decodeErr
// 				return
// 			}
// 			results = append(results, &result)
// 		}
// 	}
// 	return
// }

func (s *service) UploadDir(ctx context.Context, dir string, options *UploadOptions) (results []*UploadItemResult, err error) {
	uploadUri, err := s.parseUploadUrl(options)
	if err != nil {
		return
	}

	dir = path.Clean(dir)
	body := &bytes.Buffer{}
	multi := multipart.NewWriter(body)
	idxDir := strings.LastIndex(dir, "/")
	n := 0
	err = filepath.WalkDir(dir, func(curr string, d fs.DirEntry, err error) error {
		if err := ctx.Err(); err != nil {
			return err
		}

		if d.IsDir() {
			return nil
		}

		file, err := os.Open(curr)
		if err != nil {
			return err
		}
		defer file.Close()

		field := "file"
		if n > 0 {
			field = fmt.Sprintf("file-%d", n)
		}
		name := curr[idxDir+1:]
		part, err := multi.CreateFormFile(field, name)
		if err != nil {
			return err
		}
		_, err = io.Copy(part, file)
		n++
		return nil
	})
	if err != nil {
		return
	}

	err = multi.Close()
	if err != nil {
		return
	}

	results, err = s.doUploadRequest(ctx, uploadUri, multi.FormDataContentType(), body)
	if err != nil {
		return
	}

	if len(results) < 1 {
		err = errors.New("no result")
		return
	}

	return
}

func (s *service) doUploadRequest(ctx context.Context, uploadUri string, contentType string, body io.Reader) (results []*UploadItemResult, err error) {
	log.WithFields(log.Fields{
		"module": "btfs",
		"uri":    uploadUri,
	}).Info("do request")

	req, err := http.NewRequest("POST", uploadUri, body)
	if err != nil {
		return
	}

	req.Header.Set("Content-Type", contentType)

	timeOut := 10 * time.Minute
	if deadline, ok := ctx.Deadline(); ok {
		timeOut = deadline.Sub(time.Now())
	}
	if timeOut <= 0 {
		err = errors.New("request timeout")
		return
	}

	client := &http.Client{
		Timeout: timeOut,
	}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	decoder := json.NewDecoder(resp.Body)
	for {
		var result UploadItemResult
		err = decoder.Decode(&result)
		if err == io.EOF {
			err = nil
			break
		}
		if err != nil {
			return
		}
		results = append(results, &result)
	}
	return
}

func (s *service) parseRemoveUrl(options *RemoveOptions) (removeUrl string, err error) {
	uri, err := url.Parse(s.host)
	if err != nil {
		return
	}
	uri.Path = s.removeApiPath
	queries := uri.Query()
	queries.Set("arg", options.Hash)
	uri.RawQuery = queries.Encode()
	removeUrl = uri.String()
	return
}

func (s *service) Remove(options *RemoveOptions) (result *RemoveResult, err error) {
	removeUri, err := s.parseRemoveUrl(options)
	if err != nil {
		return
	}
	result, err = s.doRemoveRequest(removeUri)
	return
}

func (s *service) doRemoveRequest(removeUri string) (result *RemoveResult, err error) {
	log.WithFields(log.Fields{
		"module": "btfs",
		"uri":    removeUri,
	}).Info("do request")
	req, err := http.NewRequest("POST", removeUri, nil)
	if err != nil {
		return
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	r, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	result = &RemoveResult{}
	err = json.Unmarshal(r, result)

	return
}
