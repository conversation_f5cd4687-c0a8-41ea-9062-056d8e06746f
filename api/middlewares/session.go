package middlewares

import (
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
)

var sessStore sessions.Store

const (
	sessionKey    = "xSess"
	sessionSecret = "xSessSecret"
)

func init() {
	var err error
	sessStore, err = redis.NewStore(
		config.Redis.MaxIdleConn,
		config.Redis.Network,
		config.Redis.Host,
		config.Redis.Password, []byte(sessionSecret),
	)
	if err != nil {
		panic(err)
	}
}

func Session() gin.HandlerFunc {
	return sessions.Sessions(sessionKey, sessStore)
}
