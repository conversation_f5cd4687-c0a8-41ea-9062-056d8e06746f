package redis

import (
	"errors"
	"time"

	"github.com/gomodule/redigo/redis"
)

var (
	ErrLockBusy               = errors.New("lock is busy")
	ErrLockTimeoutSecsToSmall = errors.New("lock time out seconds is to small")
)

type service struct {
	pool *redis.Pool
}

type serviceOptions struct {
	Network         string
	Address         string
	Password        string
	DB              int
	MaxIdle         int
	MaxActive       int
	IdleTimeout     time.Duration
	Wait            bool
	MaxConnLifetime time.Duration
}

func newService(options *serviceOptions) (s *service, err error) {
	dial := func() (conn redis.Conn, err error) {
		return redis.Dial(
			options.Network, options.Address,
			redis.DialDatabase(options.DB),
			redis.DialPassword(options.Password),
		)
	}
	pool := &redis.Pool{
		Dial:            dial,
		MaxIdle:         options.MaxIdle,
		MaxActive:       options.MaxActive,
		IdleTimeout:     options.IdleTimeout,
		Wait:            options.Wait,
		MaxConnLifetime: options.MaxConnLifetime,
	}
	conn := pool.Get()
	defer conn.Close()
	_, err = conn.Do("PING")
	if err != nil {
		return
	}
	s = &service{
		pool: pool,
	}
	return
}

func (s *service) Close() (err error) {
	err = s.pool.Close()
	return
}

func (s *service) SetNxEx(key, val string, timeoutSecs int) (ok bool, err error) {
	conn := s.pool.Get()
	defer conn.Close()
	reply, err := conn.Do("SET", key, val, "EX", timeoutSecs, "NX")
	if err != nil {
		return
	}
	ok = reply != nil
	return
}

func (s *service) Del(key string) (n int64, err error) {
	conn := s.pool.Get()
	defer conn.Close()
	reply, err := conn.Do("DEL", key)
	if err != nil {
		return
	}
	n = reply.(int64)
	return
}

func (s *service) Expire(key string, timeoutSecs int) (ok bool, err error) {
	conn := s.pool.Get()
	defer conn.Close()
	reply, err := conn.Do("EXPIRE", key, timeoutSecs)
	if err != nil {
		return
	}
	ok = reply.(int64) == 1
	return
}

// Lock 加分布式锁，retries为自旋次数，当锁繁忙时重试加锁的次数, 每次间隔时间1秒
// timeoutSecs锁的自动保持时间，加锁线程异常挂起或结束时，锁将在该时间内自动释放
// timeoutSecs为锁的保持时间，当锁未主动释放时，到达timeoutSecs时锁将过期
// 当加锁实例进程异常结束不能正常释放锁时，锁将在该时间内自动释放
// unlock为释放函数，捕获加锁环境，调用时将释放锁
func (s *service) Lock(key string, retries, timeoutSecs int) (unlock func() error, err error) {
	if timeoutSecs < 2 {
		err = ErrLockTimeoutSecsToSmall
		return
	}
	for i := 0; i < retries+1; i++ {
		var ok bool
		ok, err = s.SetNxEx(key, "1", timeoutSecs)
		if err != nil {
			return
		}
		if ok {
			unlockCh := make(chan struct{})
			errCh := make(chan error)
			go func() {
				select {
				case <-unlockCh:
					_, dErr := s.Del(key)
					errCh <- dErr
					return
				}
			}()
			unlock = func() error {
				close(unlockCh)
				uErr := <-errCh
				return uErr
			}
			return
		}
		time.Sleep(1 * time.Second)
	}
	err = ErrLockBusy
	return
}
