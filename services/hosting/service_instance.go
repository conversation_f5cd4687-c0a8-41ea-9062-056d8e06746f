package hosting

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"math"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/docker/docker/api/types/mount"
	"github.com/google/uuid"
	random_project_generator "github.com/kevinburke/go-random-project-generator"
	"gitlab.insidebt.net/btfs/storage3-backend/btfs"
	"gitlab.insidebt.net/btfs/storage3-backend/config"
	"gitlab.insidebt.net/btfs/storage3-backend/data"
	"gitlab.insidebt.net/btfs/storage3-backend/dns"
	"gitlab.insidebt.net/btfs/storage3-backend/docker"
	"gitlab.insidebt.net/btfs/storage3-backend/github"
	"gitlab.insidebt.net/btfs/storage3-backend/log"
	"gitlab.insidebt.net/btfs/storage3-backend/model"
	"gitlab.insidebt.net/btfs/storage3-backend/redis"
	"gitlab.insidebt.net/btfs/storage3-backend/services/profits"
	"gitlab.insidebt.net/btfs/storage3-backend/tron"
)

const (
	logTimeFormat       = time.RFC3339
	buildTotalTimeLimit = 30 * time.Minute
)

var (
	ErrNotFound              = data.ErrNotFound
	ErrSiteNameExists        = errors.New("site name exists")
	ErrSiteDeleted           = errors.New("site has been deleted")
	ErrSiteBusy              = errors.New("site is busy")
	ErrOperationBusy         = errors.New("operation is busy")
	ErrDeployHasFinished     = errors.New("deploy has finished")
	ErrSiteNameReserved      = errors.New("site name contains reserved words")
	ErrStoreSizeInsufficient = errors.New("store size is insufficient")
	ErrSiteSizeInsufficient  = errors.New("site size is insufficient")
	ErrSiteNumInsufficient   = errors.New("site num is insufficient")
	YarnV2ImageMap           = map[string]struct{}{
		"btfs/common-node-18:latest":   {},
		"btfs/create-react-app:latest": {},
		"btfs/next-js:latest":          {},
	}
)

const (
	OpCodeCreateSite        = "create_site"
	DockerMountDirPrefix    = "/mnt/s3/btfs-storage/"
	DockerUserDir           = "/home/<USER>"
	CommandToActivateYarnV2 = "yarn set version berry"
)

type service struct {
	codeSourceDirPath string
}

func (s *service) QueryUserSiteList(user *model.User, transferringSiteList []string, arg *model.QuerySiteListArg) (list []*model.Site, total int64, err error) {
	return data.QuerySiteList(user.UserId, transferringSiteList, arg.Limit, arg.Offset)
}

func (s *service) GetSiteLastVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	return data.GetSiteLastVisitStatList(siteIdList)
}

func (s *service) GetSite7daysVisitStatList(siteIdList []string) (list []*model.SiteVisitStatBasic, err error) {
	return data.GetSite7daysVisitStatList(siteIdList)
}

func (s *service) GetSiteLastNVisitDaysStatList(arg *model.GetSiteDailyVisitStatListArg) (list []*model.RespSiteDailyVisitStatList, err error) {
	return data.GetSiteLastNVisitDaysStatList(arg.SiteId, arg.Days)
}

func (s *service) QueryUserSiteDetail(user *model.User, arg *model.QuerySiteDetailArg) (detail *model.Site, err error) {
	return data.QuerySiteRecord(user.UserId, arg.SiteId)
}

func (s *service) QuerySiteDetailBySiteName(arg *model.QuerySiteDetailBySiteNameArg) (detail *model.Site, err error) {
	return data.QuerySiteRecordBySiteName(arg.SiteName)
}

func (s *service) QuerySiteDetailByRepo(repoArg *model.QuerySiteDetailByRepoArg) (detail []*model.Site, err error) {
	return data.QuerySiteDetailByRepo(repoArg)
}

func (s *service) QuerySiteDetailsBySiteNames(siteNames []string) (details []*model.Site, err error) {
	return data.QuerySiteRecordsBySiteNames(siteNames)
}

func (s *service) QuerySiteDetailsBySiteIds(siteIds []string) (details []*model.Site, err error) {
	return data.QuerySiteRecordsBySiteIds(siteIds)
}

func (s *service) UpdateUserSiteName(user *model.User, arg *model.UpdateSiteNameArg) (err error) {
	if s.isSiteNameReserved(user, arg.SiteName) {
		err = ErrSiteNameReserved
		return
	}

	site, unlock, err := s.lockSite(user.UserId, arg.SiteId, 0)
	if err != nil {
		return
	}
	defer unlock()

	domain := s.getSiteDomain(arg.SiteName)
	updated, err := data.UpdateSiteName(arg.SiteId, arg.SiteName, domain)
	if err != nil {
		return
	}
	if !updated {
		err = ErrSiteNameExists
		return
	}

	err = data.AddSiteDomainHistory(&model.SiteDomainHistory{
		SiteId:   arg.SiteId,
		Domain:   domain,
		FromTime: time.Now(),
	})
	if err != nil {
		return
	}

	if site.CurrentFileHash != "" {
		dnsLink := s.getSiteDnslink(site.CurrentFileHash)
		_, err = dns.UpsertDNSLinkRecord(arg.SiteName, dnsLink)
		if err != nil {
			return
		}
		_, err = dns.DeleteDNSLinkRecord(site.SiteName, dnsLink)
		if err != nil {
			return
		}
	}

	return
}

func (s *service) UpdateUserDeploySignature(user *model.User, arg *model.UpdateDeploySignatureArg) (err error) {
	err = data.UpdateDeploySignature(user.UserId, arg.DeployId, arg.Signature, arg.SignatureMessage)
	if err != nil {
		return
	}
	return
}

func (s *service) UpdateSiteAutoDeploy(user *model.User, arg *model.UpdateSiteAutoDeployArg) (err error) {
	site, err := data.QuerySiteRecord(user.UserId, arg.SiteId)
	if err != nil {
		return
	}

	if site.IsDeleted == model.SiteDeleteYes {
		err = ErrSiteDeleted
		return
	}
	if site.IsAutoDeploy == *arg.IsAutoDeploy {
		return errors.New("site auto deploy don't change")
	}
	secret := ""
	if *arg.IsAutoDeploy {
		secret = uuid.NewString()
	}
	updated, err := data.UpdateSiteAutoDeploy(user.UserId, arg.SiteId, secret, *arg.IsAutoDeploy)
	if err != nil {
		return
	}
	if !updated {
		return errors.New("can't update site auto deploy")
	}
	return
}

func (s *service) RegenerateSiteAutoDeploySecret(user *model.User, arg *model.RegenerateSiteAutoDeploySecretArg) (err error) {
	site, err := data.QuerySiteRecord(user.UserId, arg.SiteId)
	if err != nil {
		return
	}

	if site.IsDeleted == model.SiteDeleteYes {
		err = ErrSiteDeleted
		return
	}
	if !site.IsAutoDeploy {
		return errors.New("auto deploy function is not active")
	}
	secret := uuid.NewString()
	updated, err := data.RegenerateSiteAutoDeploySecret(user.UserId, arg.SiteId, secret)
	if err != nil {
		return
	}
	if !updated {
		return errors.New("can't update site auto deploy")
	}
	return
}

func (s *service) isSiteNameReserved(user *model.User, siteName string) (reserved bool) {
	address := user.Address
	if user.Source == model.UserSourceTRON {
		address = tron.HexAddressToBase58Address(address)
	}
	source, ok := config.SiteName.ReservedWordsAllowedUserAddresses[address]
	log.WithFields(log.Fields{
		"user_address": user.Address,
		"user_source":  user.Source,
		"address":      address,
		"source":       source,
		"ok":           ok,
	}).Info("check reserved words user address")
	if ok && strings.ToUpper(source) == string(user.Source) {
		return
	}
	if _, ok := config.SiteName.ReservedWords[siteName]; !ok {
		return
	}
	reserved = true
	return
}

func (s *service) UpdateUserSiteSetting(user *model.User, arg *model.UpdateSiteSettingArg) (err error) {
	_, unlock, err := s.lockSite(user.UserId, arg.SiteId, 0)
	if err != nil {
		return
	}
	defer unlock()

	fields := &data.SiteSettingUpdateFields{
		Repo:             arg.Repo,
		Branch:           arg.Branch,
		BuildImage:       arg.BuildImage,
		BuildCommand:     arg.BuildCommand,
		PublishDirectory: arg.PublishDirectory,
		BaseDirectory:    arg.BaseDirectory,
	}

	_, err = data.UpdateSiteSetting(arg.SiteId, fields)

	return
}

func (s *service) DeleteUserSite(user *model.User, arg *model.DeleteSiteArg) (err error) {
	site, unlock, err := s.lockSite(user.UserId, arg.SiteId, 0)
	if err != nil {
		return
	}
	defer unlock()

	deleted, err := data.DeleteSiteRecord(arg.SiteId)
	if err != nil {
		return
	}

	if !deleted || site.CurrentFileHash == "" {
		return
	}
	_, err = dns.DeleteDNSLinkRecord(
		site.SiteName, s.getSiteDnslink(site.CurrentFileHash),
	)
	if err != nil {
		return
	}

	err = data.StopSiteDomainHistory(site.SiteId, time.Now())
	if err != nil {
		return
	}

	// 静默移除老的文件Hash
	_, _ = btfs.Remove(&btfs.RemoveOptions{
		Hash: site.CurrentFileHash,
	})

	return
}

func (s *service) QueryUserDeployList(user *model.User, arg *model.QueryDeployListArg) (list []*model.DeployListItem, total int64, err error) {
	return data.QueryDeployList(user.UserId, arg.SiteId, arg.Limit, arg.Offset)
}

func (s *service) GetSignedDeployList(arg *model.QueryDeployListArg) (list []*model.DeployListItemNonLogin, total int64, err error) {
	return data.GetSignedDeployList(arg.SiteId, arg.Limit, arg.Offset)
}

func (s *service) QueryUserDeployDetail(user *model.User, arg *model.QueryDeployDetailArg) (detail *model.Deploy, err error) {
	return data.QueryDeployRecord(user.UserId, arg.SiteId, arg.DeployId)
}

func (s *service) QueryUserDeployDetailByDeployId(deployId string) (detail *model.Deploy, err error) {
	return data.QueryDeployRecordByDeployId(deployId)
}

func (s *service) CreateUserSiteAndTriggerUserDeploy(user *model.User, arg *model.CreateSiteArg) (siteId, deployId string, err error) {
	now := time.Now()

	// 加锁，避免并发创建站点
	unlock, err := s.lockUserOperation(user.UserId, OpCodeCreateSite)
	if err != nil {
		return
	}
	defer unlock()

	// 检查用户可创建站点数量
	profitsStat, err := profits.GetUserProfitsStat(user)
	if err != nil {
		return
	}
	if !profitsStat.IsSuperUser && profitsStat.UsedSiteNum >= profitsStat.TotalSiteNum {
		err = ErrSiteNumInsufficient
		return
	}

	// 获取仓库最新提交id
	githubAuth := &github.Auth{
		Id:         user.GithubId,
		OauthToken: user.GithubOauthToken,
		Login:      user.GithubLogin,
	}
	latestCommitId, err := github.GetLatestCommitId(githubAuth, arg.RepoOwner, arg.Repo, arg.Branch)
	if err != nil {
		return
	}

	siteName := s.getRandomSiteName()
	domain := s.getSiteDomain(siteName)
	siteId = s.getUUID()

	if arg.SiteId == "" {
		arg.SiteId = siteId
		arg.SiteName = siteName
		err = s.insertSite(user, arg, domain)
		if err != nil {
			return
		}
	} else {
		siteId = arg.SiteId
		err = s.updateSite(user, arg)
		if err != nil {
			return
		}

	}

	err = data.AddSiteDomainHistory(&model.SiteDomainHistory{
		SiteId:   siteId,
		Domain:   domain,
		FromTime: now,
	})
	if err != nil {
		return
	}

	deployId = s.getUUID()

	// 新增创建记录
	deploy := &model.Deploy{
		DeployId:         deployId,
		UserId:           user.UserId,
		SiteId:           siteId,
		SiteName:         siteName,
		Domain:           domain,
		Status:           model.DeployStatusPending,
		RepoOwner:        arg.RepoOwner,
		Repo:             arg.Repo,
		Branch:           arg.Branch,
		CommitId:         latestCommitId,
		FileHash:         "",
		FileSize:         0,
		BuildImage:       arg.BuildImage,
		BuildCommand:     arg.BuildCommand,
		PublishDirectory: arg.PublishDirectory,
		BaseDirectory:    arg.BaseDirectory,
		Log:              "",
		Summary:          []*model.DeploySummaryItem{},
		CreatedAt:        now,
		StartedAt:        time.Time{},
		FinishedAt:       time.Time{},
		UpdatedAt:        now,
	}
	err = data.InsertDeployRecord(deploy)
	if err != nil {
		return
	}

	// 发送部署消息
	deployMessage := &model.DoDeployArg{
		UserId:   user.UserId,
		SiteId:   siteId,
		DeployId: deploy.DeployId,
	}
	err = data.SendDoDeployMessage(deployMessage)
	if err != nil {
		return
	}

	return
}

func (s *service) insertSite(user *model.User, arg *model.CreateSiteArg, domain string) error {
	siteName := arg.SiteName
	siteId := arg.SiteId

	// 新增站点记录
	site := &model.Site{
		SiteId:                  siteId,
		UserId:                  user.UserId,
		SiteName:                siteName,
		Domain:                  domain,
		RepoOwner:               arg.RepoOwner,
		Repo:                    arg.Repo,
		Branch:                  arg.Branch,
		BuildImage:              arg.BuildImage,
		BuildCommand:            arg.BuildCommand,
		PublishDirectory:        arg.PublishDirectory,
		BaseDirectory:           arg.BaseDirectory,
		LatestPublishedDeployId: "",
		CurrentFileHash:         "",
		CurrentFileSize:         0,
		LatestPublishedAt:       time.Time{},
		IsDeleted:               model.SiteDeletedNot,
		CreatedAt:               time.Now(),
		UpdatedAt:               time.Now(),
	}

	err := data.InsertSiteRecord(site)
	if err != nil {
		return err
	}
	return nil
}

func (s *service) TriggerUserDeploy(user *model.User, arg *model.TriggerDeployArg) (siteId, deployId string, err error) {
	site, unlock, err := s.lockSite(user.UserId, arg.SiteId, 0)
	if err != nil {
		return
	}
	defer unlock()

	now := time.Now()
	siteId = arg.SiteId

	// 获取仓库最新提交id
	githubAuth := &github.Auth{
		Id:         user.GithubId,
		OauthToken: user.GithubOauthToken,
		Login:      user.GithubLogin,
	}

	// 兼容旧数据
	if site.RepoOwner == "" {
		site.RepoOwner = user.GithubLogin
	}

	latestCommitId, err := github.GetLatestCommitId(githubAuth, site.RepoOwner, site.Repo, site.Branch)
	if err != nil {
		return
	}

	deployId = s.getUUID()

	// 新增创建记录
	deploy := &model.Deploy{
		DeployId:         deployId,
		UserId:           site.UserId,
		SiteId:           site.SiteId,
		SiteName:         site.SiteName,
		Domain:           site.Domain,
		Status:           model.DeployStatusPending,
		RepoOwner:        site.RepoOwner,
		Repo:             site.Repo,
		Branch:           site.Branch,
		CommitId:         latestCommitId,
		FileHash:         "",
		BuildImage:       site.BuildImage,
		BuildCommand:     site.BuildCommand,
		PublishDirectory: site.PublishDirectory,
		BaseDirectory:    site.BaseDirectory,
		Log:              "",
		Summary:          []*model.DeploySummaryItem{},
		CreatedAt:        now,
		StartedAt:        time.Time{},
		FinishedAt:       time.Time{},
		UpdatedAt:        now,
	}
	err = data.InsertDeployRecord(deploy)
	if err != nil {
		return
	}

	// 发送部署消息
	err = data.SendDoDeployMessage(&model.DoDeployArg{
		UserId:   user.UserId,
		SiteId:   site.SiteId,
		DeployId: deploy.DeployId,
	})
	if err != nil {
		return
	}

	return

}

func (s *service) DoDeploy(arg *model.DoDeployArg) (err error) {
	startDeployTime := time.Now()

	ctx, cancel := context.WithTimeout(context.Background(), buildTotalTimeLimit)
	defer cancel()

	// 加锁，禁止部署过程中对站点进行其它操作（除取消部署）
	site, unlock, err := s.lockSite(arg.UserId, arg.SiteId, 30)
	if err != nil {
		return
	}
	defer unlock()

	var (
		pending               = model.DeployStatusPending
		ongoing               = model.DeployStatusOngoing
		failed                = model.DeployStatusFailed
		insufficientStoreSize = model.DeployStatusInsufficientStoreSize
		insufficientSiteSize  = model.DeployStatusInsufficientSiteSize
	)

	// 将部署切换为执行中状态
	now := time.Now()
	nowFmt := now.Format(logTimeFormat)
	logCnt := fmt.Sprintf("%s Start deploy\n", nowFmt)
	updated, err := data.UpdateDeployRecord(
		arg.SiteId, arg.DeployId, &pending, &data.DeployUpdatedFields{
			Status:     &ongoing,
			StartedAt:  &now,
			LogContent: &logCnt,
		},
	)
	if err != nil {
		return
	}
	if !updated {
		err = errors.New("deploy status is not pending")
		return
	}

	// 追加执行错误到部署日志并切换部署为失败状态
	var (
		errFromStatus = &ongoing
		errToStatus   = &failed
		errCtx        string
	)
	defer func() {
		if err != nil && errCtx != "" {
			now := time.Now()
			nowFmt := now.Format(logTimeFormat)
			logCnt := fmt.Sprintf("%s %s: %v\n", nowFmt, errCtx, err)
			_, _ = data.UpdateDeployRecord(
				arg.SiteId, arg.DeployId, errFromStatus, &data.DeployUpdatedFields{
					Status:     errToStatus,
					FinishedAt: &now,
					LogContent: &logCnt,
				},
			)
		}
	}()

	// 查询站点用户
	user, err := data.GetUserByUserId(arg.UserId)
	if err != nil {
		errCtx = "Get user"
		return
	}

	// 查询用户权益
	profitsStat, err := profits.GetUserProfitsStat(user)
	if err != nil {
		errCtx = "Get user profits stat"
		return
	}

	// 查询最新部署详情
	deploy, err := data.QueryDeployRecord(arg.UserId, arg.SiteId, arg.DeployId)
	if err != nil {
		errCtx = "Query newest deploy record"
		return
	}

	// 确认发布目录是否存在回朔风险
	if deploy.PublishDirectory != "" && !path.IsAbs(deploy.PublishDirectory) {
		errCtx = "Check publish directory path"
		err = errors.New("publish director path must be absolute")
		return
	}

	// 拉取源码
	dst := path.Join(s.codeSourceDirPath, fmt.Sprintf("deploy-%s", deploy.DeployId))
	defer os.RemoveAll(dst) // 之后执行，无论任务成功与否，都需要清理资源
	now = time.Now()
	nowFmt = now.Format(logTimeFormat)
	githubAuth := &github.Auth{
		Id:         user.GithubId,
		OauthToken: user.GithubOauthToken,
		Login:      user.GithubLogin,
	}

	// 兼容旧数据
	if site.RepoOwner == "" {
		site.RepoOwner = user.GithubLogin
		deploy.RepoOwner = user.GithubLogin
	}

	var repoLimit int64 = math.MaxInt64
	if !profitsStat.IsSuperUser {
		repoLimit = profitsStat.TotalSiteSize
	}
	codeRoot, err := github.FetchSource(ctx, githubAuth, deploy.RepoOwner, deploy.Repo, deploy.CommitId, dst, repoLimit)
	if err == nil {
		err = s.isValidDir(codeRoot)
	}
	if err != nil {
		errCtx = "Fetch site source code from github"
		return
	}
	logCnt = fmt.Sprintf("%s Fetched site source code from github\n", nowFmt)
	summaryItem := model.DeploySummaryItem{
		Title:  "Fetched site source code from github",
		Detail: fmt.Sprintf("Start: %s, Time: %s", nowFmt, time.Now().Sub(now)),
	}
	updated, err = data.UpdateDeployRecord(
		arg.SiteId, arg.DeployId, &ongoing, &data.DeployUpdatedFields{
			LogContent:  &logCnt,
			SummaryItem: &summaryItem,
		},
	)
	if err != nil {
		return
	}
	if !updated {
		err = errors.New("deploy status is not ongoing")
		return
	}

	// 发布目录, 危险：用户可能通过相对路径向上回朔到系统文件， 比如设置为"../"，
	// 一定要确保deploy.PublishDirectory是绝对路径, 即最后上传到BTFS的publishDir只能为用户代码的根目录或该根目录的子路径
	// 否则部署任务的服务所在机器（或容器）内文件将被用户获取
	publishDir := path.Clean(path.Join(codeRoot, deploy.PublishDirectory))

	err = docker.MountVolumeToS3(codeRoot, deploy.DeployId)
	defer docker.UnmountVolumeFromS3(codeRoot, deploy.DeployId)
	if err != nil {
		return
	}

	// 编译站点源码
	if deploy.BuildImage != "" {
		workDir := path.Clean(path.Join(DockerUserDir, deploy.BaseDirectory)) // 该路径在用户指定的编译容器中执行，因此没有回朔风险
		command := deploy.BuildCommand
		// use yarn v2 to prevent fetch pakcage timeout issue.
		if _, ok := YarnV2ImageMap[deploy.BuildImage]; ok {
			// command = CommandToActivateYarnV2 + " && " + deploy.BuildCommand
			command = deploy.BuildCommand
		}
		commands := []string{"/bin/sh", "-c", command}
		mounts := []docker.Mount{{
			Type:     mount.TypeBind,
			Source:   DockerMountDirPrefix + deploy.DeployId,
			Target:   DockerUserDir,
			ReadOnly: false,
		}}
		now = time.Now()
		nowFmt = now.Format(logTimeFormat)
		handleLog := func(line string) {
			line = fmt.Sprintf("%s %s\n", time.Now().Format(logTimeFormat), line)
			_, _ = data.UpdateDeployRecord(
				arg.SiteId, arg.DeployId, &ongoing, &data.DeployUpdatedFields{
					LogContent: &line,
				},
			)
		}
		statusCode, err := docker.ContainerRunAndClean(
			ctx, deploy.BuildImage, workDir,
			commands, mounts, handleLog,
		)
		if err == nil {
			if statusCode != 0 {
				err = fmt.Errorf("build return code is not zero: %d, err:%v", statusCode, err)
			} else {
				err = docker.CopyFilesFromS3(codeRoot, deploy.DeployId, deploy.PublishDirectory)
				if err == nil {
					err = s.isValidDir(codeRoot)
				}
			}
		}
		if err != nil {
			errCtx = "Build site source code"
			return err
		}
		logCnt = fmt.Sprintf("%s Build site source code\n", nowFmt)
		summaryItem = model.DeploySummaryItem{
			Title:  "Build site source code",
			Detail: fmt.Sprintf("Start: %s, Time: %s", nowFmt, time.Now().Sub(now)),
		}
		updated, err = data.UpdateDeployRecord(
			arg.SiteId, arg.DeployId, &ongoing, &data.DeployUpdatedFields{
				LogContent:  &logCnt,
				SummaryItem: &summaryItem,
			},
		)
		if err != nil {
			return err
		}
		if !updated {
			err = errors.New("deploy status is not ongoing")
			return err
		}
	}

	// 检查用户存储空间权益
	publishSize, err := s.dirSize(publishDir)
	if err != nil {
		errCtx = "Calc publish files size"
		return
	}
	if !profitsStat.IsSuperUser && publishSize > profitsStat.TotalSiteSize {
		err = ErrSiteSizeInsufficient
		errCtx = "Check user profits"
		errToStatus = &insufficientSiteSize
		return
	}
	if !profitsStat.IsSuperUser && profitsStat.UsedStoreSize+publishSize > profitsStat.TotalStoreSize {
		err = ErrStoreSizeInsufficient
		errCtx = "Check user profits"
		errToStatus = &insufficientStoreSize
		return
	}

	// 发布站点资源到BTFS网络
	now = time.Now()
	nowFmt = now.Format(logTimeFormat)
	maxRetries := 5
	var results []*btfs.UploadItemResult
	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.Infof("*****开始上传目录尝试 [%d/%d]", attempt, maxRetries)
		results, err = btfs.UploadDir(ctx, publishDir, &btfs.UploadOptions{Pin: false})
		if err != nil || len(results) == 0 {
			log.Infof("*****上传目录失败 [尝试 %d 次]: %v", attempt, err)
			continue
		}
		log.Warnf("*****上传目录成功 [第 %d 次]", attempt)
		break
	}
	if err != nil {
		errCtx = "Upload site resource to BTFS"
		return
	}

	fileHash := results[len(results)-1].Hash
	fileSize, _ := strconv.ParseInt(results[len(results)-1].Size, 10, 64)
	resultsRaw, _ := json.Marshal(results)
	logCnt = fmt.Sprintf("%s %s\n%s Publish site resources to BTFS\n", nowFmt, resultsRaw, nowFmt)
	summaryItem = model.DeploySummaryItem{
		Title:  "Publish site resources to BTFS",
		Detail: fmt.Sprintf("Start: %s, Time: %s", nowFmt, time.Now().Sub(now)),
	}
	log.Infof("上传完成，准备更新数据库")
	updated, err = data.UpdateDeployRecord(
		arg.SiteId, arg.DeployId, &ongoing, &data.DeployUpdatedFields{
			FileHash:    &fileHash,
			FileSize:    &fileSize,
			LogContent:  &logCnt,
			SummaryItem: &summaryItem,
		},
	)
	if err != nil {
		errCtx = "Uploade Deploy Record"
		return
	}
	if !updated {
		err = errors.New("deploy status is not ongoing")
		return
	}

	// 更新域名dns解析
	dnslink := s.getSiteDnslink(fileHash)
	now = time.Now()
	nowFmt = now.Format(logTimeFormat)
	changeInfo, err := dns.UpsertDNSLinkRecord(site.SiteName, dnslink)
	if err != nil {
		errCtx = "Update site DNS"
		errFromStatus = nil
		return
	}
	changeInfoRaw, _ := json.Marshal(changeInfo)
	logCnt = fmt.Sprintf("%s %s\n%s Updated site DNS\n", nowFmt, changeInfoRaw, nowFmt)
	summaryItem = model.DeploySummaryItem{
		Title:  "Updated site dns",
		Detail: fmt.Sprintf("Start: %s, Time: %s", nowFmt, time.Now().Sub(now)),
	}
	_, err = data.UpdateDeployRecord(
		arg.SiteId, arg.DeployId, nil, &data.DeployUpdatedFields{
			Status:      &ongoing,
			SiteName:    &site.SiteName,
			Domain:      &site.Domain,
			LogContent:  &logCnt,
			SummaryItem: &summaryItem,
		},
	)
	if err != nil {
		return
	}

	finishDeployTime := time.Now()
	deploymentDuration := finishDeployTime.Sub(startDeployTime).Seconds()

	// 更新结果
	err = data.UpdateSitePublishStatus(arg.SiteId, &data.SitePublishUpdateFields{
		LatestPublishedDeployId: arg.DeployId,
		CurrentFileHash:         fileHash,
		CurrentFileSize:         fileSize,
		LatestPublishedAt:       finishDeployTime,
		DeploymentDuration:      deploymentDuration,
	})
	if err != nil {
		errCtx = "Update site publish status"
		return
	}

	// 静默移除老的文件hash
	if site.CurrentFileHash != "" {
		_, _ = btfs.Remove(&btfs.RemoveOptions{
			Hash: site.CurrentFileHash,
		})
	}

	return
}

func (s *service) CancelUserDeploy(user *model.User, arg *model.CancelDeployArg) (err error) {
	deploy, err := data.QueryDeployRecord(user.UserId, arg.SiteId, arg.DeployId)
	if err != nil {
		return
	}
	if deploy.Status == model.DeployStatusCanceled {
		return
	}
	if deploy.Status != model.DeployStatusPending && deploy.Status != model.DeployStatusOngoing {
		err = ErrDeployHasFinished
		return
	}
	var (
		canceled = model.DeployStatusCanceled
		now      = time.Now()
		logCnt   = fmt.Sprintf("Canceled at %s", now)
	)
	_, err = data.UpdateDeployRecord(arg.SiteId, arg.DeployId, nil, &data.DeployUpdatedFields{
		Status:     &canceled,
		FinishedAt: &now,
		LogContent: &logCnt,
	})
	if err != nil {
		return
	}
	_, err = redis.Del(getLockSiteKey(arg.SiteId))
	return
}

func getLockSiteKey(siteID string) string {
	return fmt.Sprintf("lock_site_%s", siteID)
}

func (s *service) QueryPresetBuildSettingList() (list []*model.PresetBuildSetting, total int64, err error) {
	return data.QueryPresetBuildSettingList()
}

func (s *service) lockSite(userId, siteId string, retries int) (site *model.Site, unlock func() error, err error) {
	// 确保用户拥有该站点
	site, err = data.QuerySiteRecord(userId, siteId)
	if err != nil {
		return
	}

	// 确保站点未被删除
	if site.IsDeleted == model.SiteDeleteYes {
		err = ErrSiteDeleted
		return
	}

	// 加分布式锁
	unlock, err = redis.Lock(getLockSiteKey(siteId), retries, 60*60)
	if err != nil {
		if err == redis.ErrLockBusy {
			err = ErrSiteBusy
		}
	}
	return
}

func (s *service) lockUserOperation(userId, opCode string) (unlock func() error, err error) {
	key := fmt.Sprintf("lock_op_%s", opCode)
	unlock, err = redis.Lock(key, 0, 60*60)
	if err != nil {
		if err == redis.ErrLockBusy {
			err = ErrOperationBusy
		}
	}
	return
}

func (s *service) getSiteDomain(siteName string) string {
	return fmt.Sprintf("%s.%s", siteName, dns.GetRootDomain())
}

func (s *service) getSiteDnslink(fileHash string) string {
	return fmt.Sprintf("/btfs/%s", fileHash)
}

func (s *service) getRandomSiteName() string {
	return random_project_generator.GenerateNumber(4)
}

func (s *service) getUUID() string {
	return uuid.New().String()
}

func (s *service) isValidDir(dir string) (err error) {
	f, err := os.Open(dir)
	if err != nil {
		return
	}
	defer f.Close()
	_, err = f.Readdirnames(1) // Or f.Readdir(1)
	return
}

func (s *service) dirSize(path string) (int64, error) {
	var size int64
	err := filepath.Walk(path, func(_ string, info fs.FileInfo, err error) error {
		if err != nil {
			log.Errorf("Error accessing path: %v", err)
			return err
		}

		// Check if info is nil (shouldn't happen unless there's a bug)
		if info == nil {
			log.Errorf("Encountered nil FileInfo for path: %s", path)
			return fmt.Errorf("nil FileInfo for path: %s", path)
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return err
	})
	return size, err
}

func (s *service) CheckUserInWhiteList(user *model.User) error {
	// return nil
	for k, v := range config.SiteName.UserAddressesWhiteList {
		lowerK := strings.ToLower(k)
		config.SiteName.UserAddressesWhiteList[lowerK] = v
	}
	_, ok := config.SiteName.UserAddressesWhiteList[strings.ToLower(user.Address)]
	if ok {
		return nil
	}
	return errors.New("you are not in the white list")
}

func (s *service) CheckBuildImageAndCommand(image, command string) error {
	if image == "" {
		return nil
	}

	presetImage, _, err := data.QueryPresetBuildSettingList()
	if err != nil {
		return err
	}

	for _, v := range presetImage {
		if v.BuildImage == image && v.BuildCommand == command {
			return nil
		}
	}
	return errors.New("only preset build image and command allowed")
}

// 更新站点的github信息
func (s *service) updateSite(user *model.User, arg *model.CreateSiteArg) error {
	site := &model.Site{
		SiteId:                  arg.SiteId,
		UserId:                  user.UserId,
		SiteName:                arg.SiteName,
		RepoOwner:               arg.RepoOwner,
		Repo:                    arg.Repo,
		Branch:                  arg.Branch,
		BuildImage:              arg.BuildImage,
		BuildCommand:            arg.BuildCommand,
		PublishDirectory:        arg.PublishDirectory,
		BaseDirectory:           arg.BaseDirectory,
		LatestPublishedDeployId: "",
		CurrentFileHash:         "",
		CurrentFileSize:         0,
		LatestPublishedAt:       time.Time{},
		IsDeleted:               model.SiteDeletedNot,
	}
	err := data.UpdateSiteGithubInfo(site)
	if err != nil {
		return err
	}
	// 更新站点转移表中的transfer_status
	return data.DeleteTransferSite(arg.SiteId, user.Address, model.TransferStatusAccepted)
}

func (s *service) GetTransferAcceptedSiteBySiteIds(siteIds []string, address string) (site []*model.SiteTransferRecord, err error) {
	return data.GetAcceptedSiteBySiteIds(siteIds, address)
}
