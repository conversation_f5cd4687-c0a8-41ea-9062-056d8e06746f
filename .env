# Log
LOG_LEVEL="info"

# DBRW
DB_RW_USER="postgres"
DB_RW_PASSWORD="123456"
DB_RW_HOST="127.0.0.1"
DB_RW_PORT="5432"
DB_RW_NAME="postgres"
DB_RW_MAX_OPEN_CONNS="32"
DB_RW_MAX_CONN_LIFE_TIME="1h"
DB_RW_MAX_CONN_IDLE_TIME="30m"

# DBRO
DB_RO_USER="postgres"
DB_RO_PASSWORD="123456"
DB_RO_HOST="127.0.0.1"
DB_RO_PORT="5432"
DB_RO_NAME="postgres"
DB_RO_MAX_OPEN_CONNS="32"
DB_RO_MAX_CONN_LIFE_TIME="1h"
DB_RO_MAX_CONN_IDLE_TIME="30m"

# RRedis
REDIS_HOST="127.0.0.1:6379"
REDIS_PASSWORD=""
REDIS_NETWORK="tcp"
REDIS_DB="0"
REDIS_MAX_IDLE_CONN="32"
REDIS_MAX_ACTIVE_CONN="32"
REDIS_CONN_IDLE_TIMEOUT="1m"
REDIS_CONN_WAIT="true"
REDIS_MAX_LIFE_TIME="1m"

# API
API_ADDR="0.0.0.0:56003"

# BTFS
BTFS_HOST="http://gateway.btfs.io:5001"
BTFS_UPLOAD_API_PATH="/api/v1/add"

# DNS
DNS_ROOT_DOMAIN="on.bttscan.net"
DNS_ZONE_ID="Z076524538CTIPKINFEGQ"

# Github App
GITHUB_APP_ID="234991"
GITHUB_CLIENT_ID="Iv1.97274d09c6cebc6e"
GITHUB_CLIENT_SECRET=""
GITHUB_PRIVATE_KEY_PATH=""
GITHUB_ENDPOINT="https://github.com/login/oauth/access_token"

# Kafka
KAFKA_BOOTSTRAP_SERVERS="127.0.0.1:9092"

# Deploy job
DEPLOY_JOB_TOPIC="t-storage3-deploy"
DEPLOY_JOB_GROUP_ID="g-storage3-deploy-v1.0"
DEPLOY_JOB_OFFSET_RESET="earliest"
DEPLOY_JOB_ROUTINES="2"

# Site name
SITE_NAME_RESERVED_WORDS="tron,justin,sunyuchen,justinsun,sunjustin,tronscan,justlend,apenft,btfs,trongrid,winklink,juststable,btt,trx,usdd,usdj,usdt,usdc,tusd,jst,wallet,scan"
SITE_NAME_RESERVED_WORDS_ALLOWED_BTTC_ADDRESSES=""
